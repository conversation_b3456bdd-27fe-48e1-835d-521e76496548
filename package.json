{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "scan": "next dev --turbopack & npx react-scan@latest localhost:3000", "build": "yarn p:m && next build", "start": "next start", "lint": "npx tsc --noEmit --skipLibCheck --project .", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio", "dup": "docker compose up -d", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "vitest", "test:unit:watch": "vitest --watch", "test:unit:coverage": "vitest --coverage", "test:integration": "jest --testPathPattern=integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "yarn test:unit && yarn test:integration && yarn test:e2e", "test:ci": "yarn test:coverage && yarn test:e2e", "playwright:install": "playwright install", "playwright:install-deps": "playwright install-deps"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@prisma/client": "6.10.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.10", "@types/bcryptjs": "^2.4.6", "@types/nprogress": "^0.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.6.2", "i18next": "^23.7.13", "ioredis": "^5.6.1", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.483.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "node-cache": "^5.1.2", "nprogress": "^0.2.0", "openai": "^5.8.2", "radix-ui": "^1.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^13.5.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3", "@jest/globals": "^30.0.3", "@next/eslint-plugin-next": "^15.3.4", "@playwright/test": "^1.53.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/identity-obj-proxy": "^3", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^4.6.0", "dotenv": "^16.4.7", "eslint": "^9.30.0", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^10.1.1", "eslint-plugin-react": "^7.37.5", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "msw": "^2.10.2", "nock": "^14.0.5", "playwright": "^1.53.1", "playwright-core": "^1.53.1", "prisma": "6.10.1", "react-scan": "^0.3.4", "supertest": "^7.1.1", "tailwindcss": "^4", "ts-jest": "^29.4.0", "typescript": "^5", "typescript-eslint": "^8.35.1", "vite": "^7.0.0", "vitest": "^3.2.4"}, "packageManager": "yarn@4.9.2"}