'use client';

import React, { useState } from 'react';
import { Button, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useSimpleFloatingUI } from '@/contexts/simple-floating-context';
import { Settings, X } from 'lucide-react';

export function SimpleClientSettings() {
	const { t } = useTranslation();
	const [isOpen, setIsOpen] = useState(false);

	// Static button content
	const settingsButton = (
		<Button
			size="icon"
			className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
			onClick={() => setIsOpen(true)}
			title={t('settings.button')}
			aria-label={t('settings.button')}
		>
			<Settings className="h-6 w-6" />
		</Button>
	);

	// Static panel content
	const settingsPanel = (
		<div className="bg-background border rounded-lg shadow-lg w-64 p-4">
			<div className="flex items-center justify-between mb-4">
				<h3 className="text-lg font-semibold">
					<Translate text="settings.title" />
				</h3>
				<Button
					size="icon"
					variant="ghost"
					className="h-8 w-8"
					onClick={() => setIsOpen(false)}
				>
					<X className="h-4 w-4" />
				</Button>
			</div>
			<div className="space-y-2">
				<Button size="sm" variant="outline" className="w-full">
					<Translate text="settings.language" />
				</Button>
				<Button size="sm" variant="outline" className="w-full">
					<Translate text="settings.theme" />
				</Button>
				<Button size="sm" variant="outline" className="w-full">
					<Translate text="settings.account" />
				</Button>
			</div>
		</div>
	);

	// Use separate floating elements for button and panel
	const { show: showButton, hide: hideButton } = useSimpleFloatingUI(
		'settings-button',
		settingsButton,
		{
			position: { bottom: 16, right: 16 },
			zIndex: 1200,
		}
	);

	const { show: showPanel, hide: hidePanel } = useSimpleFloatingUI(
		'settings-panel',
		settingsPanel,
		{
			position: { bottom: 16, right: 16 },
			zIndex: 1201,
		}
	);

	// Show button initially
	React.useEffect(() => {
		showButton();
	}, [showButton]);

	// Handle state changes
	React.useEffect(() => {
		if (isOpen) {
			hideButton();
			showPanel();
		} else {
			hidePanel();
			showButton();
		}
	}, [isOpen, showButton, hideButton, showPanel, hidePanel]);

	return null; // Content is rendered through floating UI system
}
