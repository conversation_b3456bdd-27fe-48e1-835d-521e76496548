import { OptimizedCacheOptions } from './cache.service';
import { ICacheService, getCacheService } from './cache-factory.service';

export interface SemanticCacheEntry<T = any> {
	key: string;
	value: T;
	semanticKey: string;
	keywords: string[];
	timestamp: number;
	ttl: number;
	accessCount: number;
	similarity?: number;
}

export interface SemanticSearchResult<T = any> {
	entry: SemanticCacheEntry<T>;
	similarity: number;
	exact: boolean;
}

export interface SemanticCacheConfig {
	enabled: boolean;
	similarityThreshold: number;
	maxKeywords: number;
	keywordWeight: number;
	structuralWeight: number;
	semanticWeight: number;
}

export class SemanticCacheService {
	private cacheService: ICacheService | null = null;
	private initPromise: Promise<void> | null = null;
	private semanticIndex: Map<string, SemanticCacheEntry> = new Map();
	private keywordIndex: Map<string, Set<string>> = new Map(); // keyword -> set of cache keys

	private readonly config: SemanticCacheConfig = {
		enabled: process.env.LLM_SEMANTIC_CACHE_ENABLED === 'true',
		similarityThreshold: Number.parseFloat(process.env.LLM_SEMANTIC_CACHE_THRESHOLD || '0.8'),
		maxKeywords: 20,
		keywordWeight: 0.4,
		structuralWeight: 0.3,
		semanticWeight: 0.3,
	};

	constructor() {
		this.initPromise = this.initializeCacheService();
	}

	private async initializeCacheService(): Promise<void> {
		this.cacheService = await getCacheService();
	}

	private async ensureCacheService(): Promise<ICacheService> {
		if (this.initPromise) {
			await this.initPromise;
		}
		if (!this.cacheService) {
			throw new Error('Cache service not initialized');
		}
		return this.cacheService;
	}

	/**
	 * Get value with semantic similarity matching
	 */
	async getWithSemantic<T>(
		key: string,
		params?: Record<string, any>,
		similarityThreshold?: number
	): Promise<SemanticSearchResult<T> | null> {
		// Try exact match first
		const cacheService = await this.ensureCacheService();
		const exact = await cacheService.get<T>(key);
		if (exact) {
			const entry = this.semanticIndex.get(key);
			if (entry) {
				entry.accessCount++;
				return {
					entry: entry as SemanticCacheEntry<T>,
					similarity: 1.0,
					exact: true,
				};
			}
		}

		// If semantic cache disabled, return null
		if (!this.config.enabled) {
			return null;
		}

		// Semantic similarity search
		const threshold = similarityThreshold || this.config.similarityThreshold;
		const similar = await this.findSimilarEntry<T>(key, params, threshold);

		if (similar) {
			// Update access count
			similar.entry.accessCount++;
			return similar;
		}

		return null;
	}

	/**
	 * Set value with semantic indexing
	 */
	async setWithSemantic<T>(
		key: string,
		value: T,
		params: Record<string, any> = {},
		options: OptimizedCacheOptions = {}
	): Promise<boolean> {
		// Set in regular cache first
		const cacheService = await this.ensureCacheService();
		const success = await cacheService.setOptimized(key, value, options);

		if (success && this.config.enabled) {
			// Extract semantic information
			const keywords = this.extractKeywords(key, params);
			const semanticKey = this.generateSemanticKey(params);

			// Create semantic entry
			const semanticEntry: SemanticCacheEntry<T> = {
				key,
				value,
				semanticKey,
				keywords,
				timestamp: Date.now(),
				ttl: options.ttl || 3600,
				accessCount: 0,
			};

			// Add to semantic index
			this.semanticIndex.set(key, semanticEntry);

			// Add to keyword index
			for (const keyword of keywords) {
				if (!this.keywordIndex.has(keyword)) {
					this.keywordIndex.set(keyword, new Set());
				}
				this.keywordIndex.get(keyword)!.add(key);
			}
		}

		return success;
	}

	/**
	 * Find similar cache entry
	 */
	private async findSimilarEntry<T>(
		key: string,
		params: Record<string, any> = {},
		threshold: number
	): Promise<SemanticSearchResult<T> | null> {
		const queryKeywords = this.extractKeywords(key, params);
		const querySemanticKey = this.generateSemanticKey(params);

		let bestMatch: SemanticSearchResult<T> | null = null;
		let bestSimilarity = 0;

		// Search through semantic index
		for (const [cacheKey, entry] of this.semanticIndex) {
			// Skip expired entries
			if (this.isExpired(entry)) {
				this.removeSemanticEntry(cacheKey);
				continue;
			}

			// Calculate similarity
			const similarity = this.calculateSimilarity(
				queryKeywords,
				querySemanticKey,
				entry.keywords,
				entry.semanticKey
			);

			if (similarity >= threshold && similarity > bestSimilarity) {
				bestSimilarity = similarity;
				bestMatch = {
					entry: entry as SemanticCacheEntry<T>,
					similarity,
					exact: false,
				};
			}
		}

		return bestMatch;
	}

	/**
	 * Calculate semantic similarity between queries
	 */
	private calculateSimilarity(
		queryKeywords: string[],
		querySemanticKey: string,
		cacheKeywords: string[],
		cacheSemanticKey: string
	): number {
		// Keyword similarity (Jaccard index)
		const keywordSim = this.calculateKeywordSimilarity(queryKeywords, cacheKeywords);

		// Structural similarity (based on semantic key structure)
		const structuralSim = this.calculateStructuralSimilarity(
			querySemanticKey,
			cacheSemanticKey
		);

		// Semantic similarity (simplified - could use embeddings)
		const semanticSim = this.calculateSemanticSimilarity(querySemanticKey, cacheSemanticKey);

		// Weighted combination
		return (
			keywordSim * this.config.keywordWeight +
			structuralSim * this.config.structuralWeight +
			semanticSim * this.config.semanticWeight
		);
	}

	/**
	 * Calculate keyword similarity using Jaccard index
	 */
	private calculateKeywordSimilarity(keywords1: string[], keywords2: string[]): number {
		const set1 = new Set(keywords1.map((k) => k.toLowerCase()));
		const set2 = new Set(keywords2.map((k) => k.toLowerCase()));

		const intersection = new Set([...set1].filter((x) => set2.has(x)));
		const union = new Set([...set1, ...set2]);

		return union.size > 0 ? intersection.size / union.size : 0;
	}

	/**
	 * Calculate structural similarity
	 */
	private calculateStructuralSimilarity(key1: string, key2: string): number {
		// Compare structure of semantic keys
		const parts1 = key1.split(':');
		const parts2 = key2.split(':');

		if (parts1.length !== parts2.length) {
			return 0;
		}

		let matches = 0;
		for (let i = 0; i < parts1.length; i++) {
			if (parts1[i] === parts2[i]) {
				matches++;
			}
		}

		return matches / parts1.length;
	}

	/**
	 * Calculate semantic similarity (simplified)
	 */
	private calculateSemanticSimilarity(key1: string, key2: string): number {
		// Simplified semantic similarity
		// In a real implementation, this could use word embeddings

		const words1 = this.extractWords(key1);
		const words2 = this.extractWords(key2);

		// Use synonym/related word matching
		const synonyms = this.getSynonyms();
		let semanticMatches = 0;
		let totalComparisons = 0;

		for (const word1 of words1) {
			for (const word2 of words2) {
				totalComparisons++;

				if (word1 === word2) {
					semanticMatches += 1.0;
				} else if (this.areSynonyms(word1, word2, synonyms)) {
					semanticMatches += 0.8;
				} else if (this.areRelated(word1, word2, synonyms)) {
					semanticMatches += 0.5;
				}
			}
		}

		return totalComparisons > 0 ? semanticMatches / totalComparisons : 0;
	}

	/**
	 * Extract keywords from cache key and parameters
	 */
	private extractKeywords(key: string, params: Record<string, any>): string[] {
		const keywords: string[] = [];

		// Extract from key
		const keyWords = key
			.toLowerCase()
			.split(/[^a-z0-9]/)
			.filter((word) => word.length > 2);
		keywords.push(...keyWords);

		// Extract from parameters
		for (const [_paramKey, value] of Object.entries(params)) {
			if (typeof value === 'string') {
				const words = value
					.toLowerCase()
					.split(/[^a-z0-9]/)
					.filter((word) => word.length > 2);
				keywords.push(...words);
			} else if (Array.isArray(value)) {
				for (const item of value) {
					if (typeof item === 'string') {
						const words = item
							.toLowerCase()
							.split(/[^a-z0-9]/)
							.filter((word) => word.length > 2);
						keywords.push(...words);
					}
				}
			}
		}

		// Remove duplicates and limit
		const uniqueKeywords = [...new Set(keywords)];
		return uniqueKeywords.slice(0, this.config.maxKeywords);
	}

	/**
	 * Generate semantic key for structural comparison
	 */
	private generateSemanticKey(params: Record<string, any>): string {
		const parts: string[] = [];

		// Add operation type
		if (params.operation) {
			parts.push(`op:${params.operation}`);
		}

		// Add language info
		if (params.source_language) {
			parts.push(`src:${params.source_language}`);
		}
		if (params.target_language) {
			parts.push(`tgt:${params.target_language}`);
		}

		// Add difficulty
		if (params.difficulty) {
			parts.push(`diff:${params.difficulty}`);
		}

		// Add count/size info
		if (params.count) {
			parts.push(`cnt:${params.count}`);
		}
		if (params.maxTerms) {
			parts.push(`max:${params.maxTerms}`);
		}

		return parts.join(':');
	}

	/**
	 * Extract words from text
	 */
	private extractWords(text: string): string[] {
		return text
			.toLowerCase()
			.split(/[^a-z0-9]/)
			.filter((word) => word.length > 2);
	}

	/**
	 * Get synonym mappings (simplified)
	 */
	private getSynonyms(): Record<string, string[]> {
		return {
			vocabulary: ['words', 'terms', 'lexicon'],
			generate: ['create', 'produce', 'make'],
			evaluate: ['assess', 'judge', 'analyze'],
			paragraph: ['text', 'passage', 'content'],
			question: ['query', 'inquiry', 'ask'],
			translation: ['convert', 'translate', 'transform'],
			difficulty: ['level', 'complexity', 'hardness'],
			language: ['tongue', 'speech', 'dialect'],
		};
	}

	/**
	 * Check if words are synonyms
	 */
	private areSynonyms(word1: string, word2: string, synonyms: Record<string, string[]>): boolean {
		const syns1 = synonyms[word1] || [];
		const syns2 = synonyms[word2] || [];

		return syns1.includes(word2) || syns2.includes(word1);
	}

	/**
	 * Check if words are related
	 */
	private areRelated(word1: string, word2: string, synonyms: Record<string, string[]>): boolean {
		// Check if they share common synonyms
		const syns1 = synonyms[word1] || [];
		const syns2 = synonyms[word2] || [];

		return syns1.some((syn) => syns2.includes(syn));
	}

	/**
	 * Check if entry is expired
	 */
	private isExpired(entry: SemanticCacheEntry): boolean {
		return Date.now() - entry.timestamp > entry.ttl * 1000;
	}

	/**
	 * Remove semantic entry
	 */
	private removeSemanticEntry(key: string): void {
		const entry = this.semanticIndex.get(key);
		if (entry) {
			// Remove from semantic index
			this.semanticIndex.delete(key);

			// Remove from keyword index
			for (const keyword of entry.keywords) {
				const keySet = this.keywordIndex.get(keyword);
				if (keySet) {
					keySet.delete(key);
					if (keySet.size === 0) {
						this.keywordIndex.delete(keyword);
					}
				}
			}

			// Remove from regular cache
			this.del(key);
		}
	}

	/**
	 * Clean up expired entries
	 */
	cleanupExpired(): number {
		let cleaned = 0;

		for (const [key, entry] of this.semanticIndex) {
			if (this.isExpired(entry)) {
				this.removeSemanticEntry(key);
				cleaned++;
			}
		}

		return cleaned;
	}

	/**
	 * Get semantic cache statistics
	 */
	getSemanticStats() {
		const totalEntries = this.semanticIndex.size;
		const totalKeywords = this.keywordIndex.size;

		let totalAccess = 0;
		let expiredCount = 0;

		for (const entry of this.semanticIndex.values()) {
			totalAccess += entry.accessCount;
			if (this.isExpired(entry)) {
				expiredCount++;
			}
		}

		return {
			enabled: this.config.enabled,
			totalEntries,
			totalKeywords,
			expiredCount,
			averageAccess: totalEntries > 0 ? totalAccess / totalEntries : 0,
			config: this.config,
		};
	}

	/**
	 * Find entries by keyword
	 */
	findByKeyword(keyword: string): SemanticCacheEntry[] {
		const keys = this.keywordIndex.get(keyword.toLowerCase()) || new Set();
		const entries: SemanticCacheEntry[] = [];

		for (const key of keys) {
			const entry = this.semanticIndex.get(key);
			if (entry && !this.isExpired(entry)) {
				entries.push(entry);
			}
		}

		return entries.sort((a, b) => b.accessCount - a.accessCount);
	}

	/**
	 * Delete with semantic index cleanup
	 */
	async del(key: string): Promise<boolean> {
		this.removeSemanticEntry(key);
		const cacheService = await this.ensureCacheService();
		return cacheService.del(key);
	}

	/**
	 * Flush with semantic index cleanup
	 */
	async flush(): Promise<void> {
		this.semanticIndex.clear();
		this.keywordIndex.clear();
		const cacheService = await this.ensureCacheService();
		return cacheService.flush();
	}

	// Delegation methods for basic cache operations
	async get<T>(key: string): Promise<T | null> {
		const cacheService = await this.ensureCacheService();
		return cacheService.get<T>(key);
	}

	async getOptimized<T>(key: string): Promise<T | null> {
		const cacheService = await this.ensureCacheService();
		return cacheService.getOptimized<T>(key);
	}

	async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
		const cacheService = await this.ensureCacheService();
		return cacheService.set<T>(key, value, ttl);
	}

	async setOptimized<T>(
		key: string,
		value: T,
		options?: OptimizedCacheOptions
	): Promise<boolean> {
		const cacheService = await this.ensureCacheService();
		return cacheService.setOptimized<T>(key, value, options);
	}

	async getStats(): Promise<any> {
		const cacheService = await this.ensureCacheService();
		return cacheService.getStats();
	}

	async invalidateByTag(tag: string): Promise<number> {
		const cacheService = await this.ensureCacheService();
		return cacheService.invalidateByTag(tag);
	}

	generateLLMKey(operation: string, params: Record<string, any>): string {
		// This is synchronous, so we can't use the async cache service
		// We'll use the static method from CacheService
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				// Normalize arrays and objects for consistent keys
				let value = params[key];
				if (Array.isArray(value)) {
					value = value.sort().join(',');
				} else if (typeof value === 'object' && value !== null) {
					value = JSON.stringify(value);
				}
				result[key] = value;
				return result;
			}, {});

		return `llm:${operation}:${JSON.stringify(sortedParams)}`;
	}

	generateKey(prefix: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				result[key] = params[key];
				return result;
			}, {});

		return `${prefix}:${JSON.stringify(sortedParams)}`;
	}
}

// Singleton instance
export const semanticCache = new SemanticCacheService();
