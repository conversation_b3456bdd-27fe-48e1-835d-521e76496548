import Redis from 'ioredis';
import { getRedisConfig } from '@/config';
import { CacheStats, OptimizedCacheOptions } from './cache.service';

// Cache configuration
const DEFAULT_TTL = 60 * 60 * 24; // 24 hours in seconds

export interface RedisCacheEntry<T = any> {
	value: T;
	timestamp: number;
	ttl: number;
	hits: number;
	tags?: string[];
}

export class RedisCacheService {
	private redis: Redis | null = null;
	private initPromise: Promise<void> | null = null;
	private stats: { hits: number; misses: number } = { hits: 0, misses: 0 };
	private keyPrefix: string = '';

	constructor(ttlSeconds: number = DEFAULT_TTL) {
		this.initPromise = this.initializeRedis();
	}

	private async initializeRedis(): Promise<void> {
		try {
			const config = await getRedisConfig();
			this.keyPrefix = config.keyPrefix;

			this.redis = new Redis({
				host: config.host,
				port: config.port,
				password: config.password,
				db: config.db,
				maxRetriesPerRequest: config.maxRetriesPerRequest,
				lazyConnect: config.lazyConnect,
				keepAlive: config.keepAlive,
				connectTimeout: config.connectTimeout,
				commandTimeout: config.commandTimeout,
			});

			// Handle connection events
			this.redis.on('connect', () => {
				console.log('Redis connected successfully');
			});

			this.redis.on('error', (error) => {
				console.error('Redis connection error:', error);
			});

			this.redis.on('close', () => {
				console.log('Redis connection closed');
			});

			// Test connection
			await this.redis.ping();
		} catch (error) {
			console.error('Failed to initialize Redis:', error);
			throw error;
		}
	}

	private async ensureConnected(): Promise<Redis> {
		if (this.initPromise) {
			await this.initPromise;
		}
		if (!this.redis) {
			throw new Error('Redis client not initialized');
		}
		return this.redis;
	}

	private getFullKey(key: string): string {
		return `${this.keyPrefix}${key}`;
	}

	/**
	 * Get a value from cache with enhanced tracking
	 * @param key Cache key
	 * @returns Cached value or null if not found
	 */
	async get<T>(key: string): Promise<T | null> {
		try {
			const redis = await this.ensureConnected();
			const fullKey = this.getFullKey(key);
			const value = await redis.get(fullKey);

			if (value !== null) {
				this.stats.hits++;
				return JSON.parse(value) as T;
			}

			this.stats.misses++;
			return null;
		} catch (error) {
			console.error('Redis get error:', error);
			this.stats.misses++;
			return null;
		}
	}

	/**
	 * Get value with optimized options
	 */
	async getOptimized<T>(key: string): Promise<T | null> {
		return this.get<T>(key);
	}

	/**
	 * Set a value in cache with enhanced options
	 * @param key Cache key
	 * @param value Value to cache
	 * @param ttl TTL in seconds
	 * @returns true if successful
	 */
	async set<T>(key: string, value: T, ttl: number = DEFAULT_TTL): Promise<boolean> {
		try {
			const redis = await this.ensureConnected();
			const fullKey = this.getFullKey(key);
			const serializedValue = JSON.stringify(value);

			const result = await redis.setex(fullKey, ttl, serializedValue);
			return result === 'OK';
		} catch (error) {
			console.error('Redis set error:', error);
			return false;
		}
	}

	/**
	 * Set value with optimized options
	 */
	async setOptimized<T>(
		key: string,
		value: T,
		options: OptimizedCacheOptions = {}
	): Promise<boolean> {
		const { ttl = DEFAULT_TTL, tags = [], priority = 'normal' } = options;

		// Store tags for invalidation
		if (tags.length > 0) {
			await this.addToTagIndex(key, tags);
		}

		// Adjust TTL based on priority
		const adjustedTTL = this.adjustTTLByPriority(ttl, priority);

		return this.set<T>(key, value, adjustedTTL);
	}

	/**
	 * Add key to tag index for tag-based invalidation
	 */
	private async addToTagIndex(key: string, tags: string[]): Promise<void> {
		try {
			const redis = await this.ensureConnected();

			for (const tag of tags) {
				const tagKey = this.getFullKey(`tag:${tag}`);
				await redis.sadd(tagKey, key);
				// Set expiration for tag index (longer than typical cache entries)
				await redis.expire(tagKey, 7 * 24 * 60 * 60); // 7 days
			}
		} catch (error) {
			console.error('Redis tag index error:', error);
		}
	}

	/**
	 * Adjust TTL based on priority
	 */
	private adjustTTLByPriority(baseTTL: number, priority: 'high' | 'normal' | 'low'): number {
		const multipliers = {
			high: 2.0, // High priority items live longer
			normal: 1.0, // Normal TTL
			low: 0.5, // Low priority items expire sooner
		};

		return Math.floor(baseTTL * multipliers[priority]);
	}

	/**
	 * Delete a value from cache
	 * @param key Cache key
	 * @returns true if successful
	 */
	async del(key: string): Promise<boolean> {
		try {
			const redis = await this.ensureConnected();
			const fullKey = this.getFullKey(key);
			const result = await redis.del(fullKey);
			return result > 0;
		} catch (error) {
			console.error('Redis del error:', error);
			return false;
		}
	}

	/**
	 * Clear all cache
	 */
	async flush(): Promise<void> {
		try {
			const redis = await this.ensureConnected();
			// Only flush keys with our prefix to avoid affecting other applications
			const pattern = `${this.keyPrefix}*`;
			const keys = await redis.keys(pattern);

			if (keys.length > 0) {
				await redis.del(...keys);
			}
		} catch (error) {
			console.error('Redis flush error:', error);
		}
	}

	/**
	 * Get enhanced cache stats
	 */
	async getStats(): Promise<CacheStats> {
		try {
			const redis = await this.ensureConnected();
			const info = await redis.info('memory');
			const pattern = `${this.keyPrefix}*`;
			const keys = await redis.keys(pattern);

			// Parse memory usage from Redis info
			const memoryMatch = info.match(/used_memory:(\d+)/);
			const memoryUsage = memoryMatch ? parseInt(memoryMatch[1], 10) : 0;

			const totalRequests = this.stats.hits + this.stats.misses;

			return {
				hits: this.stats.hits,
				misses: this.stats.misses,
				keys: keys.length,
				hitRate: totalRequests > 0 ? this.stats.hits / totalRequests : 0,
				memoryUsage,
			};
		} catch (error) {
			console.error('Redis stats error:', error);
			const totalRequests = this.stats.hits + this.stats.misses;
			return {
				hits: this.stats.hits,
				misses: this.stats.misses,
				keys: 0,
				hitRate: totalRequests > 0 ? this.stats.hits / totalRequests : 0,
				memoryUsage: 0,
			};
		}
	}

	/**
	 * Invalidate cache entries by tag
	 */
	async invalidateByTag(tag: string): Promise<number> {
		try {
			const redis = await this.ensureConnected();
			const tagKey = this.getFullKey(`tag:${tag}`);
			const keys = await redis.smembers(tagKey);

			if (keys.length === 0) return 0;

			// Delete all keys associated with the tag
			const fullKeys = keys.map((key) => this.getFullKey(key));
			const deletedCount = await redis.del(...fullKeys);

			// Clean up tag index
			await redis.del(tagKey);

			return deletedCount;
		} catch (error) {
			console.error('Redis invalidate by tag error:', error);
			return 0;
		}
	}

	/**
	 * Get cache key for LLM operations
	 */
	generateLLMKey(operation: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				// Normalize arrays and objects for consistent keys
				let value = params[key];
				if (Array.isArray(value)) {
					value = value.sort().join(',');
				} else if (typeof value === 'object' && value !== null) {
					value = JSON.stringify(value);
				}
				result[key] = value;
				return result;
			}, {});

		return `llm:${operation}:${JSON.stringify(sortedParams)}`;
	}

	/**
	 * Get optimized TTL for different content types
	 */
	static getOptimizedTTL(contentType: string): number {
		const ttlMap: Record<string, number> = {
			vocabulary: 7 * 24 * 60 * 60, // 7 days - stable content
			wordDetails: 7 * 24 * 60 * 60, // 7 days - stable content
			paragraphs: 3 * 24 * 60 * 60, // 3 days - semi-dynamic
			questions: 3 * 24 * 60 * 60, // 3 days - semi-dynamic
			evaluations: 30 * 24 * 60 * 60, // 30 days - very stable
			grammarPractice: 1 * 24 * 60 * 60, // 1 day - more dynamic
			default: DEFAULT_TTL,
		};

		return ttlMap[contentType] || ttlMap.default;
	}

	/**
	 * Generate a cache key from parameters
	 * @param prefix Key prefix
	 * @param params Parameters to include in the key
	 * @returns Cache key
	 */
	generateKey(prefix: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				result[key] = params[key];
				return result;
			}, {});

		return `${prefix}:${JSON.stringify(sortedParams)}`;
	}

	/**
	 * Close Redis connection
	 */
	async close(): Promise<void> {
		if (this.redis) {
			await this.redis.quit();
			this.redis = null;
		}
	}
}
