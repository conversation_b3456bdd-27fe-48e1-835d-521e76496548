/** @type {import('next').NextConfig} */
const nextConfig = {
	serverExternalPackages: ['ioredis'],
	webpack: (config, { isServer }) => {
		if (!isServer) {
			// Don't resolve 'fs', 'net', 'tls', 'dns' modules on the client-side
			config.resolve.fallback = {
				...config.resolve.fallback,
				fs: false,
				net: false,
				tls: false,
				dns: false,
				child_process: false,
				'node:fs': false,
				'node:net': false,
				'node:tls': false,
				'node:dns': false,
				'node:child_process': false,
			};
		}
		return config;
	},
};

module.exports = nextConfig;
